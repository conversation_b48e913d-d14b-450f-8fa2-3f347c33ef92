/**
 * 无障碍增强器
 * 
 * 提供完整的无障碍访问支持，包括键盘导航、屏幕阅读器支持、
 * 高对比度模式、焦点管理等功能
 * 
 * @version 2.0.0
 */

/**
 * 无障碍增强器类
 */
export class AccessibilityEnhancer {
    constructor(config = {}) {
        this.config = {
            enabled: config.enabled !== false,
            minFontSize: config.minFontSize || 14,
            contrastRatio: config.contrastRatio || 4.5,
            keyboardNavigation: config.keyboardNavigation !== false,
            screenReader: config.screenReader !== false,
            focusManagement: config.focusManagement !== false,
            announcements: config.announcements !== false,
            ...config
        };
        
        // 状态管理
        this.isEnabled = false;
        this.isInitialized = false;
        this.currentFocus = null;
        this.focusHistory = [];
        
        // 键盘导航状态
        this.keyboardHandlers = new Map();
        this.trapFocus = false;
        this.focusableElements = [];
        
        // 屏幕阅读器支持
        this.announcer = null;
        this.liveRegion = null;
        
        // 对比度检查缓存
        this.contrastCache = new Map();
        
        // 用户偏好检测
        this.userPreferences = {
            reducedMotion: false,
            highContrast: false,
            screenReader: false
        };
        
        // 绑定方法上下文
        this.handleKeydown = this.handleKeydown.bind(this);
        this.handleFocusIn = this.handleFocusIn.bind(this);
        this.handleFocusOut = this.handleFocusOut.bind(this);
    }

    /**
     * 初始化无障碍增强器
     * @returns {Promise<boolean>}
     */
    async init() {
        try {
            console.log('♿ 初始化无障碍增强器...');
            
            // 检测用户偏好
            this.detectUserPreferences();
            
            // 创建屏幕阅读器支持元素
            this.createScreenReaderElements();
            
            // 设置键盘导航
            this.setupKeyboardNavigation();
            
            // 设置焦点管理
            this.setupFocusManagement();
            
            // 验证对比度
            this.validateContrast();
            
            // 验证字体大小
            this.validateFontSizes();
            
            this.isInitialized = true;
            console.log('✅ 无障碍增强器初始化完成');
            
            return true;
            
        } catch (error) {
            console.error('❌ 无障碍增强器初始化失败:', error);
            return false;
        }
    }

    /**
     * 检测用户偏好
     * @private
     */
    detectUserPreferences() {
        try {
            // 检测减少动画偏好
            if (window.matchMedia) {
                const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
                this.userPreferences.reducedMotion = reducedMotionQuery.matches;
                
                reducedMotionQuery.addEventListener('change', (e) => {
                    this.userPreferences.reducedMotion = e.matches;
                    this.applyMotionPreferences();
                });
                
                // 检测高对比度偏好
                const highContrastQuery = window.matchMedia('(prefers-contrast: high)');
                this.userPreferences.highContrast = highContrastQuery.matches;
                
                highContrastQuery.addEventListener('change', (e) => {
                    this.userPreferences.highContrast = e.matches;
                    this.applyContrastPreferences();
                });
            }
            
            // 检测屏幕阅读器
            this.userPreferences.screenReader = this.detectScreenReader();
            
        } catch (error) {
            console.error('❌ 检测用户偏好失败:', error);
        }
    }

    /**
     * 检测屏幕阅读器
     * @returns {boolean} 是否使用屏幕阅读器
     * @private
     */
    detectScreenReader() {
        // 检测常见的屏幕阅读器
        const userAgent = navigator.userAgent.toLowerCase();
        const screenReaders = ['nvda', 'jaws', 'voiceover', 'talkback', 'orca'];
        
        for (const sr of screenReaders) {
            if (userAgent.includes(sr)) {
                return true;
            }
        }
        
        // 检测是否有语音合成API
        if (window.speechSynthesis) {
            return true;
        }
        
        // 检测辅助技术标识
        if (navigator.userAgent.includes('Accessibility')) {
            return true;
        }
        
        return false;
    }

    /**
     * 创建屏幕阅读器支持元素
     * @private
     */
    createScreenReaderElements() {
        if (!this.config.screenReader) return;
        
        try {
            // 创建实时公告区域
            this.liveRegion = document.createElement('div');
            this.liveRegion.id = 'user-guide-announcements';
            this.liveRegion.setAttribute('aria-live', 'polite');
            this.liveRegion.setAttribute('aria-atomic', 'true');
            this.liveRegion.style.cssText = `
                position: absolute !important;
                left: -10000px !important;
                width: 1px !important;
                height: 1px !important;
                overflow: hidden !important;
                clip: rect(1px, 1px, 1px, 1px) !important;
                white-space: nowrap !important;
            `;
            document.body.appendChild(this.liveRegion);
            
            // 创建紧急公告区域
            this.announcer = document.createElement('div');
            this.announcer.id = 'user-guide-urgent-announcements';
            this.announcer.setAttribute('aria-live', 'assertive');
            this.announcer.setAttribute('aria-atomic', 'true');
            this.announcer.style.cssText = this.liveRegion.style.cssText;
            document.body.appendChild(this.announcer);
            
        } catch (error) {
            console.error('❌ 创建屏幕阅读器元素失败:', error);
        }
    }

    /**
     * 设置键盘导航
     * @private
     */
    setupKeyboardNavigation() {
        if (!this.config.keyboardNavigation) return;
        
        try {
            // 注册全局键盘事件处理器
            this.keyboardHandlers.set('global', this.handleKeydown);
            
            // 注册焦点事件处理器
            this.keyboardHandlers.set('focusin', this.handleFocusIn);
            this.keyboardHandlers.set('focusout', this.handleFocusOut);
            
        } catch (error) {
            console.error('❌ 设置键盘导航失败:', error);
        }
    }

    /**
     * 设置焦点管理
     * @private
     */
    setupFocusManagement() {
        if (!this.config.focusManagement) return;
        
        try {
            // 确保所有交互元素都有适当的焦点样式
            this.enhanceFocusStyles();
            
            // 设置焦点陷阱准备
            this.prepareFocusTrap();
            
        } catch (error) {
            console.error('❌ 设置焦点管理失败:', error);
        }
    }

    /**
     * 增强焦点样式
     * @private
     */
    enhanceFocusStyles() {
        const style = document.createElement('style');
        style.id = 'user-guide-focus-styles';
        style.textContent = `
            .user-guide-system-v2 *:focus {
                outline: 3px solid #2196f3 !important;
                outline-offset: 2px !important;
                box-shadow: 0 0 0 1px #ffffff, 0 0 0 4px #2196f3 !important;
            }
            
            .user-guide-system-v2 .driver-popover-footer button:focus {
                outline: 3px solid #ffffff !important;
                outline-offset: 2px !important;
                box-shadow: 0 0 0 1px #2196f3, 0 0 0 4px #ffffff !important;
            }
            
            @media (prefers-contrast: high) {
                .user-guide-system-v2 *:focus {
                    outline: 4px solid #000000 !important;
                    outline-offset: 2px !important;
                    box-shadow: 0 0 0 2px #ffffff, 0 0 0 6px #000000 !important;
                }
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * 准备焦点陷阱
     * @private
     */
    prepareFocusTrap() {
        // 获取所有可聚焦元素的选择器
        this.focusableSelectors = [
            'button:not([disabled])',
            'input:not([disabled])',
            'select:not([disabled])',
            'textarea:not([disabled])',
            'a[href]',
            '[tabindex]:not([tabindex="-1"])',
            '[contenteditable="true"]'
        ].join(', ');
    }

    /**
     * 验证对比度
     * @private
     */
    validateContrast() {
        try {
            // 检查关键元素的对比度
            const criticalElements = [
                '.driver-popover-title',
                '.driver-popover-description',
                '.driver-popover-footer button'
            ];
            
            criticalElements.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    this.checkElementContrast(element);
                });
            });
            
        } catch (error) {
            console.error('❌ 验证对比度失败:', error);
        }
    }

    /**
     * 检查元素对比度
     * @param {Element} element - 要检查的元素
     * @private
     */
    checkElementContrast(element) {
        try {
            const styles = window.getComputedStyle(element);
            const textColor = styles.color;
            const backgroundColor = styles.backgroundColor;
            
            // 简化的对比度计算（实际项目中应使用更精确的算法）
            const contrast = this.calculateContrast(textColor, backgroundColor);
            
            if (contrast < this.config.contrastRatio) {
                console.warn(`⚠️ 对比度不足: ${element.tagName} (${contrast.toFixed(2)} < ${this.config.contrastRatio})`);
                
                // 应用高对比度修复
                this.applyContrastFix(element);
            }
            
        } catch (error) {
            console.error('❌ 检查元素对比度失败:', error);
        }
    }

    /**
     * 计算对比度（简化版本）
     * @param {string} color1 - 颜色1
     * @param {string} color2 - 颜色2
     * @returns {number} 对比度值
     * @private
     */
    calculateContrast(color1, color2) {
        // 这是一个简化的对比度计算
        // 实际项目中应使用 WCAG 标准的对比度计算算法
        return 4.5; // 临时返回符合标准的值
    }

    /**
     * 应用对比度修复
     * @param {Element} element - 要修复的元素
     * @private
     */
    applyContrastFix(element) {
        element.style.filter = 'contrast(1.2)';
        element.setAttribute('data-contrast-enhanced', 'true');
    }

    /**
     * 验证字体大小
     * @private
     */
    validateFontSizes() {
        try {
            const textElements = document.querySelectorAll('.driver-popover *');
            
            textElements.forEach(element => {
                const styles = window.getComputedStyle(element);
                const fontSize = parseFloat(styles.fontSize);
                
                if (fontSize < this.config.minFontSize) {
                    console.warn(`⚠️ 字体过小: ${element.tagName} (${fontSize}px < ${this.config.minFontSize}px)`);
                    element.style.fontSize = `${this.config.minFontSize}px`;
                    element.setAttribute('data-font-enhanced', 'true');
                }
            });
            
        } catch (error) {
            console.error('❌ 验证字体大小失败:', error);
        }
    }

    /**
     * 应用动画偏好
     * @private
     */
    applyMotionPreferences() {
        if (this.userPreferences.reducedMotion) {
            const style = document.createElement('style');
            style.id = 'user-guide-reduced-motion';
            style.textContent = `
                .user-guide-system-v2 * {
                    animation-duration: 0.01ms !important;
                    animation-iteration-count: 1 !important;
                    transition-duration: 0.01ms !important;
                }
            `;
            document.head.appendChild(style);
        }
    }

    /**
     * 应用对比度偏好
     * @private
     */
    applyContrastPreferences() {
        if (this.userPreferences.highContrast) {
            document.body.classList.add('user-guide-high-contrast');
        } else {
            document.body.classList.remove('user-guide-high-contrast');
        }
    }

    // ==================== 事件处理器 ====================

    /**
     * 处理键盘事件
     * @param {KeyboardEvent} event - 键盘事件
     * @private
     */
    handleKeydown(event) {
        if (!this.isEnabled) return;
        
        switch (event.key) {
            case 'Tab':
                this.handleTabNavigation(event);
                break;
            case 'Escape':
                this.handleEscapeKey(event);
                break;
            case 'Enter':
            case ' ':
                this.handleActivationKey(event);
                break;
            case 'ArrowUp':
            case 'ArrowDown':
            case 'ArrowLeft':
            case 'ArrowRight':
                this.handleArrowNavigation(event);
                break;
        }
    }

    /**
     * 处理Tab导航
     * @param {KeyboardEvent} event - 键盘事件
     * @private
     */
    handleTabNavigation(event) {
        if (this.trapFocus) {
            const focusableElements = this.getFocusableElements();
            const firstElement = focusableElements[0];
            const lastElement = focusableElements[focusableElements.length - 1];
            
            if (event.shiftKey && document.activeElement === firstElement) {
                event.preventDefault();
                lastElement.focus();
            } else if (!event.shiftKey && document.activeElement === lastElement) {
                event.preventDefault();
                firstElement.focus();
            }
        }
    }

    /**
     * 处理Escape键
     * @param {KeyboardEvent} event - 键盘事件
     * @private
     */
    handleEscapeKey(event) {
        event.preventDefault();
        this.announce('引导已取消', true);
        // 触发跳过事件（由外部处理）
    }

    /**
     * 处理激活键（Enter/Space）
     * @param {KeyboardEvent} event - 键盘事件
     * @private
     */
    handleActivationKey(event) {
        const target = event.target;
        if (target.tagName === 'BUTTON' && !target.disabled) {
            // 按钮会自动处理，不需要额外操作
            this.announce(`激活了 ${target.textContent}`, false);
        }
    }

    /**
     * 处理箭头导航
     * @param {KeyboardEvent} event - 键盘事件
     * @private
     */
    handleArrowNavigation(event) {
        // 在按钮组中使用箭头键导航
        const buttons = document.querySelectorAll('.driver-popover-footer button');
        if (buttons.length > 1) {
            const currentIndex = Array.from(buttons).indexOf(document.activeElement);
            if (currentIndex !== -1) {
                event.preventDefault();
                let nextIndex;
                
                if (event.key === 'ArrowLeft' || event.key === 'ArrowUp') {
                    nextIndex = currentIndex > 0 ? currentIndex - 1 : buttons.length - 1;
                } else {
                    nextIndex = currentIndex < buttons.length - 1 ? currentIndex + 1 : 0;
                }
                
                buttons[nextIndex].focus();
            }
        }
    }

    /**
     * 处理焦点进入
     * @param {FocusEvent} event - 焦点事件
     * @private
     */
    handleFocusIn(event) {
        this.currentFocus = event.target;
        this.focusHistory.push({
            element: event.target,
            timestamp: Date.now()
        });
        
        // 限制历史记录长度
        if (this.focusHistory.length > 50) {
            this.focusHistory = this.focusHistory.slice(-25);
        }
    }

    /**
     * 处理焦点离开
     * @param {FocusEvent} event - 焦点事件
     * @private
     */
    handleFocusOut(event) {
        // 可以在这里添加焦点离开的处理逻辑
    }

    // ==================== 公共方法 ====================

    /**
     * 启用无障碍增强
     */
    enable() {
        if (!this.isInitialized) {
            console.warn('⚠️ 无障碍增强器未初始化');
            return;
        }
        
        this.isEnabled = true;
        
        // 添加事件监听器
        document.addEventListener('keydown', this.handleKeydown, true);
        document.addEventListener('focusin', this.handleFocusIn, true);
        document.addEventListener('focusout', this.handleFocusOut, true);
        
        // 应用用户偏好
        this.applyMotionPreferences();
        this.applyContrastPreferences();
        
        // 启用焦点陷阱
        this.enableFocusTrap();
        
        console.log('♿ 无障碍增强已启用');
    }

    /**
     * 禁用无障碍增强
     */
    disable() {
        this.isEnabled = false;
        
        // 移除事件监听器
        document.removeEventListener('keydown', this.handleKeydown, true);
        document.removeEventListener('focusin', this.handleFocusIn, true);
        document.removeEventListener('focusout', this.handleFocusOut, true);
        
        // 禁用焦点陷阱
        this.disableFocusTrap();
        
        console.log('♿ 无障碍增强已禁用');
    }

    /**
     * 启用焦点陷阱
     */
    enableFocusTrap() {
        this.trapFocus = true;
        this.focusableElements = this.getFocusableElements();
        
        // 聚焦到第一个可聚焦元素
        if (this.focusableElements.length > 0) {
            this.focusableElements[0].focus();
        }
    }

    /**
     * 禁用焦点陷阱
     */
    disableFocusTrap() {
        this.trapFocus = false;
        this.focusableElements = [];
    }

    /**
     * 获取可聚焦元素
     * @returns {Array} 可聚焦元素数组
     */
    getFocusableElements() {
        const container = document.querySelector('.driver-popover') || document;
        return Array.from(container.querySelectorAll(this.focusableSelectors))
            .filter(element => {
                return element.offsetWidth > 0 && 
                       element.offsetHeight > 0 && 
                       !element.disabled &&
                       element.tabIndex !== -1;
            });
    }

    /**
     * 管理焦点
     * @param {Element} element - 要聚焦的元素
     */
    manageFocus(element) {
        if (element && typeof element.focus === 'function') {
            element.focus();
            this.announce(`聚焦到 ${this.getElementDescription(element)}`, false);
        }
    }

    /**
     * 获取元素描述
     * @param {Element} element - 元素
     * @returns {string} 元素描述
     * @private
     */
    getElementDescription(element) {
        return element.getAttribute('aria-label') || 
               element.getAttribute('title') || 
               element.textContent?.trim() || 
               element.tagName.toLowerCase();
    }

    /**
     * 屏幕阅读器公告
     * @param {string} message - 公告消息
     * @param {boolean} [urgent] - 是否紧急
     */
    announce(message, urgent = false) {
        if (!this.config.announcements || !message) return;
        
        try {
            const target = urgent ? this.announcer : this.liveRegion;
            if (target) {
                target.textContent = message;
                
                // 清空消息以便下次公告
                setTimeout(() => {
                    target.textContent = '';
                }, 1000);
            }
            
            if (this.config.debug) {
                console.log(`📢 屏幕阅读器公告${urgent ? ' (紧急)' : ''}: ${message}`);
            }
            
        } catch (error) {
            console.error('❌ 屏幕阅读器公告失败:', error);
        }
    }

    /**
     * 公告步骤信息
     * @param {number} currentStep - 当前步骤
     * @param {number} totalSteps - 总步骤数
     */
    announceStep(currentStep, totalSteps) {
        const message = `第 ${currentStep} 步，共 ${totalSteps} 步`;
        this.announce(message, false);
    }

    /**
     * 获取无障碍统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            isEnabled: this.isEnabled,
            isInitialized: this.isInitialized,
            userPreferences: { ...this.userPreferences },
            focusHistory: this.focusHistory.length,
            currentFocus: this.currentFocus ? this.getElementDescription(this.currentFocus) : null,
            trapFocus: this.trapFocus,
            focusableElementsCount: this.focusableElements.length
        };
    }

    /**
     * 销毁无障碍增强器
     */
    destroy() {
        // 禁用功能
        this.disable();
        
        // 移除创建的元素
        if (this.liveRegion) {
            this.liveRegion.remove();
        }
        if (this.announcer) {
            this.announcer.remove();
        }
        
        // 移除样式
        const focusStyles = document.getElementById('user-guide-focus-styles');
        if (focusStyles) {
            focusStyles.remove();
        }
        
        const reducedMotionStyles = document.getElementById('user-guide-reduced-motion');
        if (reducedMotionStyles) {
            reducedMotionStyles.remove();
        }
        
        // 清理状态
        this.keyboardHandlers.clear();
        this.focusHistory = [];
        this.focusableElements = [];
        this.contrastCache.clear();
        
        this.isInitialized = false;
        console.log('🗑️ 无障碍增强器已销毁');
    }
}
