/**
 * 用户引导系统核心样式
 * 
 * 基于现代CSS技术的高性能、无障碍友好的样式系统
 * 
 * @version 2.0.0
 */

/* ===========================
   CSS自定义属性 - 主题系统
   ========================== */

.user-guide-system-v2 {
    /* 颜色系统 */
    --guide-primary: #2196f3;
    --guide-primary-hover: #1976d2;
    --guide-primary-light: #42a5f5;
    --guide-primary-pale: #e3f2fd;
    
    --guide-surface: #ffffff;
    --guide-surface-hover: #f5f5f5;
    --guide-surface-elevated: #ffffff;
    
    --guide-text-primary: #212121;
    --guide-text-secondary: #757575;
    --guide-text-tertiary: #9e9e9e;
    --guide-text-inverse: #ffffff;
    
    --guide-border: #e0e0e0;
    --guide-border-light: #f0f0f0;
    --guide-border-focus: #2196f3;
    
    --guide-shadow: rgba(0, 0, 0, 0.1);
    --guide-shadow-hover: rgba(0, 0, 0, 0.15);
    --guide-shadow-focus: rgba(33, 150, 243, 0.25);
    
    --guide-success: #4caf50;
    --guide-error: #f44336;
    --guide-warning: #ff9800;
    
    /* 字体系统 */
    --guide-font-family: "PingFang SC", "Helvetica Neue", -apple-system, BlinkMacSystemFont, sans-serif;
    --guide-font-size-xs: 12px;
    --guide-font-size-sm: 14px;
    --guide-font-size-base: 16px;
    --guide-font-size-lg: 18px;
    --guide-font-size-xl: 20px;
    --guide-font-size-2xl: 24px;
    
    /* 间距系统 */
    --guide-spacing-xs: 4px;
    --guide-spacing-sm: 8px;
    --guide-spacing-base: 16px;
    --guide-spacing-lg: 24px;
    --guide-spacing-xl: 32px;
    
    /* 圆角系统 */
    --guide-radius-sm: 4px;
    --guide-radius-base: 6px;
    --guide-radius-lg: 8px;
    --guide-radius-xl: 12px;
    
    /* 动画系统 */
    --guide-animation-duration: 0.3s;
    --guide-animation-easing: cubic-bezier(0.4, 0, 0.2, 1);
    
    /* 遮罩层配置 */
    --guide-overlay-color: rgba(0, 0, 0, 0.75);
    --guide-overlay-blur: 4px;
    
    /* Z-index 层级 */
    --guide-z-overlay: 999998;
    --guide-z-highlighted: 999999;
    --guide-z-popover: 1000000;
}

/* ===========================
   遮罩层样式 - 半透明深色
   ========================== */

.user-guide-system-v2 .driver-overlay {
    background: var(--guide-overlay-color) !important;
    backdrop-filter: blur(var(--guide-overlay-blur)) !important;
    transition: all var(--guide-animation-duration) var(--guide-animation-easing) !important;
    z-index: var(--guide-z-overlay) !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    pointer-events: auto !important;
}

/* 高对比度模式的遮罩 */
.user-guide-system-v2.high-contrast .driver-overlay {
    background: rgba(0, 0, 0, 0.9) !important;
    backdrop-filter: none !important;
}

/* ===========================
   高亮元素样式
   ========================== */

.user-guide-system-v2 .driver-highlighted-element {
    position: relative !important;
    z-index: var(--guide-z-highlighted) !important;
    border-radius: var(--guide-radius-lg) !important;
    transition: all var(--guide-animation-duration) var(--guide-animation-easing) !important;
    
    /* 高亮效果 */
    box-shadow: 
        0 0 0 4px var(--guide-primary),
        0 0 0 8px rgba(33, 150, 243, 0.3),
        0 0 20px rgba(33, 150, 243, 0.4) !important;
}

/* 高亮元素的脉冲动画 */
@keyframes guide-highlight-pulse {
    0%, 100% {
        box-shadow: 
            0 0 0 4px var(--guide-primary),
            0 0 0 8px rgba(33, 150, 243, 0.3),
            0 0 20px rgba(33, 150, 243, 0.4);
    }
    50% {
        box-shadow: 
            0 0 0 4px var(--guide-primary),
            0 0 0 12px rgba(33, 150, 243, 0.2),
            0 0 30px rgba(33, 150, 243, 0.6);
    }
}

.user-guide-system-v2 .driver-highlighted-element {
    animation: guide-highlight-pulse 2s ease-in-out infinite !important;
}

/* ===========================
   弹出框主体样式
   ========================== */

.user-guide-system-v2 .driver-popover {
    /* 基础样式 */
    background: var(--guide-surface) !important;
    border: none !important;
    border-radius: var(--guide-radius-xl) !important;
    font-family: var(--guide-font-family) !important;
    
    /* 阴影效果 */
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.15),
        0 8px 20px rgba(0, 0, 0, 0.10),
        0 0 0 1px var(--guide-border-light) !important;
    
    /* 尺寸控制 */
    max-width: min(420px, 90vw) !important;
    min-width: min(320px, 85vw) !important;
    width: auto !important;
    
    /* 层级和定位 */
    position: fixed !important;
    z-index: var(--guide-z-popover) !important;
    
    /* 动画效果 */
    animation: guide-popover-enter var(--guide-animation-duration) var(--guide-animation-easing) !important;
    transform-origin: center !important;
    
    /* 内容溢出处理 */
    overflow: hidden !important;
}

/* 弹出框进入动画 */
@keyframes guide-popover-enter {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* ===========================
   弹出框标题样式
   ========================== */

.user-guide-system-v2 .driver-popover-title {
    /* 字体样式 */
    color: var(--guide-text-primary) !important;
    font-size: var(--guide-font-size-xl) !important;
    font-weight: 700 !important;
    line-height: 1.3 !important;
    
    /* 间距 */
    margin: 0 !important;
    padding: var(--guide-spacing-lg) var(--guide-spacing-lg) var(--guide-spacing-sm) var(--guide-spacing-lg) !important;
    
    /* 渐变文字效果 */
    background: linear-gradient(135deg, var(--guide-primary), var(--guide-primary-light)) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
}

/* ===========================
   弹出框描述样式
   ========================== */

.user-guide-system-v2 .driver-popover-description {
    /* 字体样式 */
    color: var(--guide-text-secondary) !important;
    font-size: var(--guide-font-size-base) !important;
    font-weight: 400 !important;
    line-height: 1.6 !important;
    
    /* 间距 */
    margin: 0 !important;
    padding: 0 var(--guide-spacing-lg) var(--guide-spacing-base) var(--guide-spacing-lg) !important;
}

/* 强调文字样式 */
.user-guide-system-v2 .driver-popover-description strong {
    color: var(--guide-primary) !important;
    font-weight: 700 !important;
    background: var(--guide-primary-pale) !important;
    padding: 2px 4px !important;
    border-radius: var(--guide-radius-sm) !important;
}

/* ===========================
   弹出框底部样式
   ========================== */

.user-guide-system-v2 .driver-popover-footer {
    /* 布局 */
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    gap: var(--guide-spacing-sm) !important;
    flex-wrap: wrap !important;
    
    /* 样式 */
    padding: var(--guide-spacing-sm) var(--guide-spacing-lg) var(--guide-spacing-lg) var(--guide-spacing-lg) !important;
    border-top: 1px solid var(--guide-border-light) !important;
    background: var(--guide-surface) !important;
    border-radius: 0 0 var(--guide-radius-xl) var(--guide-radius-xl) !important;
}

/* 进度指示器 */
.user-guide-system-v2 .driver-popover-progress-text {
    color: var(--guide-primary) !important;
    font-size: var(--guide-font-size-sm) !important;
    font-weight: 600 !important;
    background: var(--guide-primary-pale) !important;
    padding: var(--guide-spacing-sm) var(--guide-spacing-base) !important;
    border-radius: var(--guide-radius-lg) !important;
    border: 1px solid var(--guide-primary-light) !important;
}

/* ===========================
   按钮样式系统
   ========================== */

.user-guide-system-v2 .driver-popover-footer button {
    /* 基础样式 */
    font-family: var(--guide-font-family) !important;
    font-size: var(--guide-font-size-sm) !important;
    font-weight: 600 !important;
    line-height: 1.4 !important;
    
    /* 尺寸 */
    padding: 10px 20px !important;
    min-width: 80px !important;
    height: 40px !important;
    
    /* 边框和圆角 */
    border: 2px solid transparent !important;
    border-radius: var(--guide-radius-base) !important;
    
    /* 交互 */
    cursor: pointer !important;
    transition: all var(--guide-animation-duration) var(--guide-animation-easing) !important;
    
    /* 文字处理 */
    white-space: nowrap !important;
    text-overflow: ellipsis !important;
    overflow: hidden !important;
}

/* 主要按钮（下一步/完成） */
.user-guide-system-v2 .driver-popover-next-btn,
.user-guide-system-v2 .driver-popover-done-btn {
    background: linear-gradient(135deg, var(--guide-primary), var(--guide-primary-hover)) !important;
    color: var(--guide-text-inverse) !important;
    border-color: var(--guide-primary) !important;
    box-shadow: 
        0 4px 12px var(--guide-shadow),
        0 2px 4px rgba(33, 150, 243, 0.3) !important;
}

.user-guide-system-v2 .driver-popover-next-btn:hover,
.user-guide-system-v2 .driver-popover-done-btn:hover {
    background: linear-gradient(135deg, var(--guide-primary-hover), var(--guide-primary)) !important;
    transform: translateY(-2px) !important;
    box-shadow: 
        0 8px 20px var(--guide-shadow-hover),
        0 4px 8px rgba(33, 150, 243, 0.4) !important;
}

.user-guide-system-v2 .driver-popover-next-btn:focus,
.user-guide-system-v2 .driver-popover-done-btn:focus {
    outline: none !important;
    box-shadow: 
        0 4px 12px var(--guide-shadow),
        0 0 0 3px var(--guide-shadow-focus) !important;
}

/* 次要按钮（上一步） */
.user-guide-system-v2 .driver-popover-prev-btn {
    background: var(--guide-surface) !important;
    color: var(--guide-primary) !important;
    border-color: var(--guide-primary) !important;
}

.user-guide-system-v2 .driver-popover-prev-btn:hover {
    background: var(--guide-primary-pale) !important;
    border-color: var(--guide-primary-hover) !important;
    color: var(--guide-primary-hover) !important;
    transform: translateY(-1px) !important;
}

/* 关闭按钮 */
.user-guide-system-v2 .driver-popover-close-btn {
    background: var(--guide-surface) !important;
    color: var(--guide-text-secondary) !important;
    border-color: var(--guide-border) !important;
}

.user-guide-system-v2 .driver-popover-close-btn:hover {
    color: var(--guide-error) !important;
    border-color: var(--guide-error) !important;
    background: #ffebee !important;
    transform: translateY(-1px) !important;
}

/* ===========================
   响应式设计
   ========================== */

/* 平板设备 */
@media (max-width: 768px) {
    .user-guide-system-v2 .driver-popover {
        max-width: 95vw !important;
        min-width: 90vw !important;
    }
    
    .user-guide-system-v2 .driver-popover-title {
        font-size: var(--guide-font-size-lg) !important;
        padding: var(--guide-spacing-base) var(--guide-spacing-base) var(--guide-spacing-sm) var(--guide-spacing-base) !important;
    }
    
    .user-guide-system-v2 .driver-popover-description {
        font-size: var(--guide-font-size-sm) !important;
        padding: 0 var(--guide-spacing-base) var(--guide-spacing-sm) var(--guide-spacing-base) !important;
    }
    
    .user-guide-system-v2 .driver-popover-footer {
        flex-direction: column !important;
        gap: var(--guide-spacing-xs) !important;
    }
    
    .user-guide-system-v2 .driver-popover-footer button {
        width: 100% !important;
        max-width: none !important;
    }
}

/* 手机设备 */
@media (max-width: 480px) {
    .user-guide-system-v2 .driver-popover-title {
        font-size: var(--guide-font-size-base) !important;
    }
    
    .user-guide-system-v2 .driver-popover-description {
        font-size: var(--guide-font-size-sm) !important;
    }
    
    .user-guide-system-v2 .driver-popover-footer button {
        font-size: var(--guide-font-size-xs) !important;
        padding: 8px 16px !important;
        height: 36px !important;
    }
}

/* ===========================
   无障碍访问增强
   ========================== */

/* 焦点样式 */
.user-guide-system-v2 *:focus {
    outline: 3px solid var(--guide-border-focus) !important;
    outline-offset: 2px !important;
    box-shadow: 0 0 0 1px var(--guide-surface), 0 0 0 4px var(--guide-border-focus) !important;
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .user-guide-system-v2 {
        --guide-overlay-color: rgba(0, 0, 0, 0.95);
        --guide-text-primary: #000000;
        --guide-text-secondary: #000000;
        --guide-border: #000000;
    }
    
    .user-guide-system-v2 .driver-popover {
        border: 3px solid #000000 !important;
    }
    
    .user-guide-system-v2 .driver-highlighted-element {
        box-shadow: 0 0 0 4px #000000, 0 0 0 8px #ffffff !important;
    }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
    .user-guide-system-v2 * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ===========================
   特殊步骤样式
   ========================== */

/* 欢迎步骤 */
.user-guide-system-v2 .guide-step-welcome .driver-popover {
    text-align: center !important;
    max-width: 400px !important;
}

.user-guide-system-v2 .guide-step-welcome .driver-popover-title {
    font-size: var(--guide-font-size-2xl) !important;
    background: linear-gradient(135deg, var(--guide-primary), #9c27b0) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
}

/* 完成步骤 */
.user-guide-system-v2 .guide-step-complete .driver-popover {
    text-align: center !important;
    max-width: 380px !important;
}

.user-guide-system-v2 .guide-step-complete .driver-popover-title {
    color: var(--guide-success) !important;
}

/* AI助手步骤 */
.user-guide-system-v2 .guide-step-ai .driver-popover-title {
    background: linear-gradient(135deg, var(--guide-primary), #673ab7) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
}

/* ===========================
   工具类
   ========================== */

/* 隐藏元素 */
.user-guide-hidden {
    display: none !important;
}

/* 引导激活时的body样式 */
.user-guide-active {
    overflow: hidden !important;
}

/* 调试模式样式 */
.user-guide-debug {
    position: fixed !important;
    bottom: 20px !important;
    left: 20px !important;
    background: rgba(0, 0, 0, 0.9) !important;
    color: white !important;
    padding: 12px !important;
    border-radius: var(--guide-radius-base) !important;
    font-family: monospace !important;
    font-size: var(--guide-font-size-xs) !important;
    z-index: calc(var(--guide-z-popover) + 1) !important;
    max-width: 300px !important;
}
